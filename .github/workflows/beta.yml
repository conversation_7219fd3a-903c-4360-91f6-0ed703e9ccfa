name: MoviePilot Builder Beta
on:
  workflow_dispatch:

jobs:
  Docker-build:
    runs-on: ubuntu-latest
    name: Build Docker Image
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Release version
        id: release_version
        run: |
          app_version=$(cat version.py |sed -ne "s/APP_VERSION\s=\s'v\(.*\)'/\1/gp")
          echo "app_version=$app_version" >> $GITHUB_ENV

      - name: Docker Meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: |
            ${{ secrets.DOCKER_USERNAME }}/moviepilot-v2
            ghcr.io/${{ github.repository }}
          tags: |
            type=raw,value=beta

      - name: Set Up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set Up Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Login GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build Image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: docker/Dockerfile
          platforms: |
            linux/amd64
            linux/arm64/v8
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha, scope=${{ github.workflow }}-docker
          cache-to: type=gha, scope=${{ github.workflow }}-docker

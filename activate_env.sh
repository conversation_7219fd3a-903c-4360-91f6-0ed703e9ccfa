#!/bin/bash

# MoviePilot 虚拟环境激活脚本
# 使用方法: source activate_env.sh

echo "正在激活 MoviePilot 虚拟环境..."

# 检查虚拟环境是否存在
if [ ! -d "venv" ]; then
    echo "错误: 虚拟环境不存在，请先运行 python -m venv venv 创建虚拟环境"
    return 1
fi

# 激活虚拟环境
source venv/bin/activate

# 验证激活是否成功
if [ "$VIRTUAL_ENV" != "" ]; then
    echo "✅ 虚拟环境已激活: $VIRTUAL_ENV"
    echo "Python 版本: $(python --version)"
    echo "Python 路径: $(which python)"
    echo ""
    echo "常用命令:"
    echo "  - 退出虚拟环境: deactivate"
    echo "  - 安装依赖: pip install -r requirements.txt"
    echo "  - 运行应用: python app/main.py"
    echo "  - 运行测试: python -m pytest tests/"
else
    echo "❌ 虚拟环境激活失败"
    return 1
fi
